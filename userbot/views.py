import asyncio
from django.urls import reverse
from django.contrib import messages
from django.core.cache import cache
from django.shortcuts import redirect
from django.utils import timezone

from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

from django_celery_beat.models import PeriodicTask, IntervalSchedule
import json

from userbot.models import Session, ScheduledMessage
from userbot.service import userbot
from userbot.external.telethon.message_sender import send_message_to_auto_folder


# pylint: disable=E1101
def login_with_phone(request):
    """
    View to login with a phone number.
    """
    code = request.POST.get("code")
    session_name = request.POST.get("session_name")
    phone_code_hash = cache.get(f"phone_code_hash_{session_name}")

    try:
        session_obj = Session.get_by_session_name(session_name)
    except Session.DoesNotExist:
        messages.error(
            request,
            f"Session {session_name} does not exist",
        )
        url = reverse("admin:userbot_session_changelist")
        return redirect(url)

    try:
        userbot.verify_code(
            session_name=session_name,
            code=code,
            phone_code_hash=phone_code_hash,
            two_fa_password=session_obj.two_fa_password,
        )
        url = reverse("admin:userbot_session_changelist")
        messages.success(
            request,
            f"Session {session_name} verified successfully",
        )
        session_obj.mark_as_active()
        return redirect(url)

    # pylint: disable=broad-exception-caught
    except Exception as exc:
        messages.error(
            request,
            f"Invalid verification code. for {session_name} error: {exc}",
        )
        url = reverse("admin:userbot_session_changelist")
        return redirect(url)


@api_view(['POST'])
def send_auto_message(request):
    """
    API endpoint to send message to AUTO folder chats

    Accepts:
        - user_id: Telegram user ID (integer)
        - text: Message text to send (string)

    Returns:
        - success: boolean
        - message/error: string
        - results: dict with sending results
    """
    try:
        user_id = request.data.get('user_id')
        text = request.data.get('text')

        # Validate input
        if not user_id:
            return Response({
                'success': False,
                'error': 'user_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not text:
            return Response({
                'success': False,
                'error': 'text is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user_id = int(user_id)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'user_id must be a valid integer'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            _ = Session.objects.get(user_id=user_id, is_active=True)
        except Session.DoesNotExist:
            return Response({
                'success': False,
                'error': f'No active session found for user_id: {user_id}',
                'results': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        result = asyncio.run(send_message_to_auto_folder(user_id, text))

        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def create_scheduled_message(request):
    """
    API endpoint to create a scheduled message task

    Accepts:
        - user_id: Telegram user ID (integer)
        - text: Message text to send (string)
        - interval_minutes: Interval in minutes between sends (integer)

    Returns:
        - success: boolean
        - message/error: string
        - scheduled_message_id: integer (if successful)
        - task_id: string (Celery task ID if successful)
    """
    try:
        user_id = request.data.get('user_id')
        text = request.data.get('text')
        interval_minutes = request.data.get('interval_minutes')

        # Validate input
        if not user_id:
            return Response({
                'success': False,
                'error': 'user_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not text:
            return Response({
                'success': False,
                'error': 'text is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not interval_minutes:
            return Response({
                'success': False,
                'error': 'interval_minutes is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user_id = int(user_id)
            interval_minutes = int(interval_minutes)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'user_id and interval_minutes must be valid integers'
            }, status=status.HTTP_400_BAD_REQUEST)

        if interval_minutes < 1:
            return Response({
                'success': False,
                'error': 'interval_minutes must be at least 1'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if session exists and is active
        try:
            session = Session.objects.get(user_id=user_id, is_active=True)
        except Session.DoesNotExist:
            return Response({
                'success': False,
                'error': f'No active session found for user_id: {user_id}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create ScheduledMessage record
        scheduled_message = ScheduledMessage.objects.create(
            user_id=user_id,
            text=text,
            interval_minutes=interval_minutes,
            is_active=True,
            status='pending'
        )

        # Create or get interval schedule
        schedule, created = IntervalSchedule.objects.get_or_create(
            every=interval_minutes,
            period=IntervalSchedule.MINUTES,
        )

        # Create periodic task
        task_name = f"scheduled_message_{scheduled_message.id}_{user_id}"
        periodic_task = PeriodicTask.objects.create(
            interval=schedule,
            name=task_name,
            task='userbot.tasks.send_scheduled_message',
            args=json.dumps([user_id, text, scheduled_message.id]),
            enabled=True,
        )

        # Update scheduled message with task ID
        scheduled_message.celery_task_id = periodic_task.name
        scheduled_message.save()

        return Response({
            'success': True,
            'message': 'Scheduled message created successfully',
            'scheduled_message_id': scheduled_message.id,
            'task_id': periodic_task.name,
            'interval_minutes': interval_minutes,
            'next_run_time': periodic_task.next_run_time
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def stop_scheduled_message(request):
    """
    API endpoint to stop a scheduled message task

    Accepts:
        - scheduled_message_id: ID of the scheduled message (integer)

    Returns:
        - success: boolean
        - message/error: string
    """
    try:
        scheduled_message_id = request.data.get('scheduled_message_id')

        if not scheduled_message_id:
            return Response({
                'success': False,
                'error': 'scheduled_message_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            scheduled_message_id = int(scheduled_message_id)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'scheduled_message_id must be a valid integer'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get scheduled message
        try:
            scheduled_message = ScheduledMessage.objects.get(id=scheduled_message_id)
        except ScheduledMessage.DoesNotExist:
            return Response({
                'success': False,
                'error': f'Scheduled message with ID {scheduled_message_id} not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Disable the periodic task
        if scheduled_message.celery_task_id:
            try:
                periodic_task = PeriodicTask.objects.get(name=scheduled_message.celery_task_id)
                periodic_task.enabled = False
                periodic_task.save()
            except PeriodicTask.DoesNotExist:
                pass  # Task might have been deleted manually

        # Update scheduled message status
        scheduled_message.is_active = False
        scheduled_message.status = 'cancelled'
        scheduled_message.save()

        return Response({
            'success': True,
            'message': 'Scheduled message stopped successfully',
            'scheduled_message_id': scheduled_message.id
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def list_scheduled_messages(request):
    """
    API endpoint to list all scheduled messages for a user

    Query parameters:
        - user_id: Telegram user ID (optional, if not provided returns all)
        - is_active: Filter by active status (optional, true/false)

    Returns:
        - success: boolean
        - scheduled_messages: list of scheduled message objects
    """
    try:
        user_id = request.GET.get('user_id')
        is_active = request.GET.get('is_active')

        # Build query
        queryset = ScheduledMessage.objects.all()

        if user_id:
            try:
                user_id = int(user_id)
                queryset = queryset.filter(user_id=user_id)
            except (ValueError, TypeError):
                return Response({
                    'success': False,
                    'error': 'user_id must be a valid integer'
                }, status=status.HTTP_400_BAD_REQUEST)

        if is_active is not None:
            is_active_bool = is_active.lower() in ['true', '1', 'yes']
            queryset = queryset.filter(is_active=is_active_bool)

        # Serialize data
        scheduled_messages = []
        for msg in queryset.order_by('-created_at'):
            scheduled_messages.append({
                'id': msg.id,
                'user_id': msg.user_id,
                'text': msg.text,
                'interval_minutes': msg.interval_minutes,
                'is_active': msg.is_active,
                'status': msg.status,
                'sent_count': msg.sent_count,
                'last_sent_at': msg.last_sent_at.isoformat() if msg.last_sent_at else None,
                'created_at': msg.created_at.isoformat(),
                'error_message': msg.error_message,
                'celery_task_id': msg.celery_task_id
            })

        return Response({
            'success': True,
            'scheduled_messages': scheduled_messages,
            'count': len(scheduled_messages)
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
